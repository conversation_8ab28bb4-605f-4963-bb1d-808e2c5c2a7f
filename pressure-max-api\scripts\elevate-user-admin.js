#!/usr/bin/env node

/**
 * Admin Utility Script: Elevate User to Admin Status
 * 
 * This script elevates a user to admin status in the Supabase database.
 * Usage: node scripts/elevate-user-admin.js <email>
 * Example: node scripts/elevate-user-admin.js <EMAIL>
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  console.error('❌ Error: SUPABASE_URL environment variable is required');
  process.exit(1);
}

if (!supabaseServiceKey || supabaseServiceKey === 'your-service-role-key-here') {
  console.error('❌ Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  console.error('   Please get your service role key from Supabase Dashboard > Settings > API');
  console.error('   and update the .env file');
  process.exit(1);
}

// Create Supabase admin client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Elevate user to admin status
 */
async function elevateUserToAdmin(email) {
  try {
    console.log(`🔍 Looking for user with email: ${email}`);
    
    // First, check if user exists in auth.users
    const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
    
    if (authError) {
      throw new Error(`Failed to list auth users: ${authError.message}`);
    }
    
    const authUser = authUsers.users.find(user => user.email === email);
    
    if (!authUser) {
      console.error(`❌ User with email ${email} not found in auth.users`);
      console.log('Available users:');
      authUsers.users.forEach(user => {
        console.log(`  - ${user.email} (ID: ${user.id})`);
      });
      return false;
    }
    
    console.log(`✅ Found auth user: ${authUser.email} (ID: ${authUser.id})`);
    
    // Check if user profile exists
    const { data: existingProfile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('email', email)
      .single();
    
    if (profileError && profileError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new Error(`Failed to check user profile: ${profileError.message}`);
    }
    
    if (!existingProfile) {
      console.log(`📝 Creating user profile for ${email}`);
      
      // Create user profile with admin role
      const { data: newProfile, error: createError } = await supabaseAdmin
        .from('user_profiles')
        .insert({
          id: authUser.id,
          email: authUser.email,
          first_name: authUser.user_metadata?.first_name || null,
          last_name: authUser.user_metadata?.last_name || null,
          role: 'admin',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        })
        .select()
        .single();
      
      if (createError) {
        throw new Error(`Failed to create user profile: ${createError.message}`);
      }
      
      console.log(`✅ Created user profile with admin role for ${email}`);
      console.log(`   Profile ID: ${newProfile.id}`);
      console.log(`   Role: ${newProfile.role}`);
      
    } else {
      console.log(`📝 Updating existing user profile for ${email}`);
      console.log(`   Current role: ${existingProfile.role}`);
      
      if (existingProfile.role === 'admin') {
        console.log(`ℹ️  User ${email} is already an admin`);
        return true;
      }
      
      // Update user role to admin
      const { data: updatedProfile, error: updateError } = await supabaseAdmin
        .from('user_profiles')
        .update({
          role: 'admin',
          updated_at: new Date()
        })
        .eq('email', email)
        .select()
        .single();
      
      if (updateError) {
        throw new Error(`Failed to update user role: ${updateError.message}`);
      }
      
      console.log(`✅ Successfully elevated ${email} to admin status`);
      console.log(`   Previous role: ${existingProfile.role}`);
      console.log(`   New role: ${updatedProfile.role}`);
    }
    
    // Log security event
    try {
      await supabaseAdmin
        .from('security_events')
        .insert({
          user_id: authUser.id,
          event_type: 'role_elevation',
          event_data: {
            email: email,
            new_role: 'admin',
            elevated_by: 'admin_script',
            timestamp: new Date()
          },
          ip_address: '127.0.0.1',
          user_agent: 'admin-script',
          created_at: new Date()
        });
      
      console.log(`📝 Logged security event for role elevation`);
    } catch (logError) {
      console.warn(`⚠️  Warning: Failed to log security event: ${logError.message}`);
    }
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error elevating user to admin: ${error.message}`);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  const email = process.argv[2];
  
  if (!email) {
    console.error('❌ Error: Email address is required');
    console.log('Usage: node scripts/elevate-user-admin.js <email>');
    console.log('Example: node scripts/elevate-user-admin.js <EMAIL>');
    process.exit(1);
  }
  
  console.log('🚀 Admin User Elevation Script');
  console.log('================================');
  console.log(`Target email: ${email}`);
  console.log('');
  
  const success = await elevateUserToAdmin(email);
  
  if (success) {
    console.log('');
    console.log('🎉 User elevation completed successfully!');
    console.log(`   ${email} now has admin privileges`);
  } else {
    console.log('');
    console.log('💥 User elevation failed');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { elevateUserToAdmin };
