const { supabaseAdmin } = require('../config/supabase');
const FacebookTokenManager = require('./facebookTokenManager');

class FacebookPermissionsService {
  constructor() {
    this.requiredPermissions = [
      'ads_management',
      'ads_read',
      'read_insights',
      'leads_retrieval',
      'pages_read_engagement',
      'pages_manage_ads'
    ];

    this.optionalPermissions = [
      'business_management',
      'pages_show_list'
    ];

    this.apiVersion = process.env.FACEBOOK_API_VERSION || 'v18.0';
  }

  /**
   * Verify user has all required permissions
   */
  async verifyPermissions(workspaceId) {
    try {
      const { token, connection } = await FacebookTokenManager.getDecryptedToken(workspaceId);

      // Get current permissions from Facebook
      const response = await fetch(`https://graph.facebook.com/${this.apiVersion}/me/permissions?access_token=${token}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API error: ${data.error.message}`);
      }

      const grantedPermissions = data.data
        .filter(perm => perm.status === 'granted')
        .map(perm => perm.permission);

      // Check required permissions
      const missingRequired = this.requiredPermissions.filter(
        perm => !grantedPermissions.includes(perm)
      );

      const hasAllRequired = missingRequired.length === 0;

      // Update connection with permission status
      await supabaseAdmin
        .from('facebook_connections')
        .update({
          permissions_granted: grantedPermissions,
          permissions_ok: hasAllRequired,
          last_error: hasAllRequired ? null : `Missing permissions: ${missingRequired.join(', ')}`,
          updated_at: new Date()
        })
        .eq('workspace_id', workspaceId);

      return {
        valid: hasAllRequired,
        granted: grantedPermissions,
        missing_required: missingRequired,
        missing_optional: this.optionalPermissions.filter(
          perm => !grantedPermissions.includes(perm)
        )
      };
    } catch (error) {
      console.error('Permission verification error:', error);
      
      // Update connection with error status
      await supabaseAdmin
        .from('facebook_connections')
        .update({
          permissions_ok: false,
          last_error: error.message,
          updated_at: new Date()
        })
        .eq('workspace_id', workspaceId);

      throw error;
    }
  }

  /**
   * Verify user has admin access to specific ad account
   */
  async verifyAdAccountAccess(workspaceId, adAccountId) {
    try {
      const { token } = await FacebookTokenManager.getDecryptedToken(workspaceId);

      // Get user's role on the ad account
      const response = await fetch(
        `https://graph.facebook.com/${this.apiVersion}/act_${adAccountId}/assigned_users?fields=user,role,tasks&access_token=${token}`
      );
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API error: ${data.error.message}`);
      }

      // Find current user in assigned users
      const userResponse = await fetch(`https://graph.facebook.com/${this.apiVersion}/me?access_token=${token}`);
      const userData = await userResponse.json();
      
      if (userData.error) {
        throw new Error(`Facebook API error: ${userData.error.message}`);
      }

      const currentUserId = userData.id;
      const userAssignment = data.data.find(assignment => assignment.user.id === currentUserId);

      if (!userAssignment) {
        return {
          hasAccess: false,
          reason: 'User not assigned to ad account'
        };
      }

      // Check if user has sufficient permissions
      const requiredTasks = ['MANAGE', 'ADVERTISE', 'ANALYZE'];
      const userTasks = userAssignment.tasks || [];
      const hasRequiredTasks = requiredTasks.every(task => userTasks.includes(task));

      return {
        hasAccess: hasRequiredTasks,
        role: userAssignment.role,
        tasks: userTasks,
        reason: hasRequiredTasks ? 'Access granted' : 'Insufficient permissions on ad account'
      };
    } catch (error) {
      console.error('Ad account access verification error:', error);
      throw error;
    }
  }

  /**
   * Verify user has admin access to Facebook page
   */
  async verifyPageAccess(workspaceId, pageId) {
    try {
      const { token } = await FacebookTokenManager.getDecryptedToken(workspaceId);

      // Get user's role on the page
      const response = await fetch(
        `https://graph.facebook.com/${this.apiVersion}/${pageId}?fields=access_token,roles.limit(100){user,role}&access_token=${token}`
      );
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API error: ${data.error.message}`);
      }

      // Get current user info
      const userResponse = await fetch(`https://graph.facebook.com/${this.apiVersion}/me?access_token=${token}`);
      const userData = await userResponse.json();
      
      if (userData.error) {
        throw new Error(`Facebook API error: ${userData.error.message}`);
      }

      const currentUserId = userData.id;
      const userRole = data.roles?.data?.find(role => role.user.id === currentUserId);

      if (!userRole) {
        return {
          hasAccess: false,
          reason: 'User not assigned to page'
        };
      }

      // Check if user has admin role
      const hasAdminAccess = userRole.role === 'ADMIN';

      return {
        hasAccess: hasAdminAccess,
        role: userRole.role,
        reason: hasAdminAccess ? 'Admin access granted' : 'Admin role required'
      };
    } catch (error) {
      console.error('Page access verification error:', error);
      throw error;
    }
  }

  /**
   * Get available ad accounts for user
   */
  async getAvailableAdAccounts(workspaceId) {
    try {
      const { token } = await FacebookTokenManager.getDecryptedToken(workspaceId);

      const response = await fetch(
        `https://graph.facebook.com/${this.apiVersion}/me/adaccounts?fields=id,name,account_status,currency,timezone_name&access_token=${token}`
      );
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API error: ${data.error.message}`);
      }

      // Filter for active accounts only
      const activeAccounts = data.data.filter(account => 
        account.account_status === 1 // 1 = ACTIVE
      );

      return activeAccounts.map(account => ({
        id: account.id.replace('act_', ''), // Remove act_ prefix
        name: account.name,
        currency: account.currency,
        timezone: account.timezone_name
      }));
    } catch (error) {
      console.error('Get ad accounts error:', error);
      throw error;
    }
  }

  /**
   * Get available Facebook pages for user
   */
  async getAvailablePages(workspaceId) {
    try {
      const { token } = await FacebookTokenManager.getDecryptedToken(workspaceId);

      const response = await fetch(
        `https://graph.facebook.com/${this.apiVersion}/me/accounts?fields=id,name,category,access_token&access_token=${token}`
      );
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API error: ${data.error.message}`);
      }

      return data.data.map(page => ({
        id: page.id,
        name: page.name,
        category: page.category
      }));
    } catch (error) {
      console.error('Get pages error:', error);
      throw error;
    }
  }

  /**
   * Complete permission and access verification
   */
  async completeVerification(workspaceId, adAccountId, pageId) {
    try {
      // Verify basic permissions
      const permissionCheck = await this.verifyPermissions(workspaceId);
      
      if (!permissionCheck.valid) {
        return {
          success: false,
          error: 'Missing required permissions',
          details: permissionCheck
        };
      }

      // Verify ad account access
      const adAccountCheck = await this.verifyAdAccountAccess(workspaceId, adAccountId);
      
      if (!adAccountCheck.hasAccess) {
        return {
          success: false,
          error: 'Insufficient ad account access',
          details: adAccountCheck
        };
      }

      // Verify page access
      const pageCheck = await this.verifyPageAccess(workspaceId, pageId);
      
      if (!pageCheck.hasAccess) {
        return {
          success: false,
          error: 'Insufficient page access',
          details: pageCheck
        };
      }

      // Update connection with verified assets
      await supabaseAdmin
        .from('facebook_connections')
        .update({
          selected_ad_account_id: adAccountId,
          selected_page_id: pageId,
          permissions_ok: true,
          connection_status: 'connected',
          last_error: null,
          updated_at: new Date()
        })
        .eq('workspace_id', workspaceId);

      return {
        success: true,
        permissions: permissionCheck,
        adAccount: adAccountCheck,
        page: pageCheck
      };
    } catch (error) {
      console.error('Complete verification error:', error);
      throw error;
    }
  }

  /**
   * Generate permission request URL
   */
  generatePermissionRequestUrl(redirectUri) {
    const permissions = [...this.requiredPermissions, ...this.optionalPermissions].join(',');
    const baseUrl = 'https://www.facebook.com/v18.0/dialog/oauth';
    
    const params = new URLSearchParams({
      client_id: process.env.FACEBOOK_APP_ID,
      redirect_uri: redirectUri,
      scope: permissions,
      response_type: 'code',
      state: crypto.randomBytes(16).toString('hex')
    });

    return `${baseUrl}?${params.toString()}`;
  }
}

module.exports = new FacebookPermissionsService();
