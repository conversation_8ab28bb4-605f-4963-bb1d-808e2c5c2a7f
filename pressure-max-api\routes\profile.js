const express = require('express');
const { supabaseAdmin } = require('../config/supabase');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

/**
 * Get user profile with workspace information
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user profile
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      return res.status(404).json({
        error: 'Profile not found',
        code: 'PROFILE_NOT_FOUND'
      });
    }

    // Get user's workspaces
    const { data: workspaces, error: workspacesError } = await supabaseAdmin
      .from('workspaces')
      .select(`
        *,
        subscriptions(*),
        facebook_connections(*)
      `)
      .eq('owner_id', userId);

    if (workspacesError) {
      console.error('Error fetching workspaces:', workspacesError);
    }

    res.json({
      success: true,
      profile,
      workspaces: workspaces || []
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch profile',
      code: 'PROFILE_FETCH_FAILED'
    });
  }
});

/**
 * Update user profile
 */
router.put('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      full_name,
      avatar_url,
      business_name,
      business_logo_url,
      business_website,
      phone
    } = req.body;

    // Update user profile
    const { data: updatedProfile, error: updateError } = await supabaseAdmin
      .from('user_profiles')
      .update({
        full_name,
        avatar_url,
        business_name,
        business_logo_url,
        business_website,
        phone,
        updated_at: new Date()
      })
      .eq('id', userId)
      .select()
      .single();

    if (updateError) {
      return res.status(400).json({
        error: 'Failed to update profile',
        details: updateError.message,
        code: 'PROFILE_UPDATE_FAILED'
      });
    }

    // Log security event
    await supabaseAdmin
      .from('security_events')
      .insert({
        user_id: userId,
        event_type: 'profile_updated',
        event_data: {
          updated_fields: Object.keys(req.body),
          timestamp: new Date()
        },
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

    res.json({
      success: true,
      profile: updatedProfile,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      error: 'Failed to update profile',
      code: 'PROFILE_UPDATE_FAILED'
    });
  }
});

/**
 * Create new workspace
 */
router.post('/workspace', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      business_name,
      business_logo_url,
      business_website,
      business_phone,
      business_address,
      business_city,
      business_state,
      business_zip,
      service_areas,
      specialties
    } = req.body;

    if (!business_name) {
      return res.status(400).json({
        error: 'Business name is required',
        code: 'BUSINESS_NAME_REQUIRED'
      });
    }

    // Create workspace
    const { data: workspace, error: workspaceError } = await supabaseAdmin
      .from('workspaces')
      .insert({
        owner_id: userId,
        business_name,
        business_logo_url,
        business_website,
        business_phone,
        business_address,
        business_city,
        business_state,
        business_zip,
        service_areas: service_areas || [],
        specialties: specialties || ['residential']
      })
      .select()
      .single();

    if (workspaceError) {
      return res.status(400).json({
        error: 'Failed to create workspace',
        details: workspaceError.message,
        code: 'WORKSPACE_CREATE_FAILED'
      });
    }

    // Create default subscription (trial)
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 14); // 14-day trial

    await supabaseAdmin
      .from('subscriptions')
      .insert({
        workspace_id: workspace.id,
        status: 'trial',
        plan_name: 'basic',
        trial_ends_at: trialEndDate
      });

    res.json({
      success: true,
      workspace,
      message: 'Workspace created successfully'
    });

  } catch (error) {
    console.error('Workspace creation error:', error);
    res.status(500).json({
      error: 'Failed to create workspace',
      code: 'WORKSPACE_CREATE_FAILED'
    });
  }
});

/**
 * Update workspace
 */
router.put('/workspace/:workspaceId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { workspaceId } = req.params;
    const updateData = req.body;

    // Verify ownership
    const { data: workspace, error: verifyError } = await supabaseAdmin
      .from('workspaces')
      .select('owner_id')
      .eq('id', workspaceId)
      .single();

    if (verifyError || workspace.owner_id !== userId) {
      return res.status(403).json({
        error: 'Workspace not found or access denied',
        code: 'WORKSPACE_ACCESS_DENIED'
      });
    }

    // Update workspace
    const { data: updatedWorkspace, error: updateError } = await supabaseAdmin
      .from('workspaces')
      .update({
        ...updateData,
        updated_at: new Date()
      })
      .eq('id', workspaceId)
      .select()
      .single();

    if (updateError) {
      return res.status(400).json({
        error: 'Failed to update workspace',
        details: updateError.message,
        code: 'WORKSPACE_UPDATE_FAILED'
      });
    }

    res.json({
      success: true,
      workspace: updatedWorkspace,
      message: 'Workspace updated successfully'
    });

  } catch (error) {
    console.error('Workspace update error:', error);
    res.status(500).json({
      error: 'Failed to update workspace',
      code: 'WORKSPACE_UPDATE_FAILED'
    });
  }
});

/**
 * Get workspace analytics
 */
router.get('/workspace/:workspaceId/analytics', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { workspaceId } = req.params;

    // Verify ownership
    const { data: workspace, error: verifyError } = await supabaseAdmin
      .from('workspaces')
      .select('owner_id')
      .eq('id', workspaceId)
      .single();

    if (verifyError || workspace.owner_id !== userId) {
      return res.status(403).json({
        error: 'Workspace not found or access denied',
        code: 'WORKSPACE_ACCESS_DENIED'
      });
    }

    // Get analytics using the database function
    const { data: analytics, error: analyticsError } = await supabaseAdmin
      .rpc('get_workspace_analytics', { workspace_id: workspaceId });

    if (analyticsError) {
      console.error('Analytics error:', analyticsError);
      return res.status(500).json({
        error: 'Failed to fetch analytics',
        code: 'ANALYTICS_FETCH_FAILED'
      });
    }

    res.json({
      success: true,
      analytics: analytics[0] || {
        total_campaigns: 0,
        active_campaigns: 0,
        total_leads: 0,
        total_spend: 0,
        avg_cpl: 0
      }
    });

  } catch (error) {
    console.error('Analytics fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch analytics',
      code: 'ANALYTICS_FETCH_FAILED'
    });
  }
});

module.exports = router;
