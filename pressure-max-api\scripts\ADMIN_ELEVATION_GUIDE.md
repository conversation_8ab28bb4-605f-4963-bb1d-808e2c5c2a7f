# Admin User Elevation Guide

This guide explains how to elevate a user to admin status in your Supabase database.

## Method 1: Using the Admin Script (Recommended)

### Prerequisites

1. **Get your Supabase Service Role Key:**
   - Go to your Supabase Dashboard: https://supabase.com/dashboard
   - Select your project: `pocxgdrtvwxjaurqhoua`
   - Navigate to **Settings** > **API**
   - Copy the **service_role** key (starts with `eyJ...`)
   - Update your `.env` file:
     ```env
     SUPABASE_SERVICE_ROLE_KEY=eyJ_your_actual_service_role_key_here
     ```

### Run the Script

```bash
# Navigate to the API directory
cd pressure-max-api

# Run the elevation script
node scripts/elevate-user-admin.js <EMAIL>
```

The script will:
- ✅ Find the user in the auth system
- ✅ Create or update their user profile
- ✅ Set their role to 'admin'
- ✅ Log the security event

## Method 2: Using Supabase Dashboard (Alternative)

If you prefer to use the Supabase dashboard directly:

1. **Go to Supabase Dashboard:**
   - Visit: https://supabase.com/dashboard/project/pocxgdrtvwxjaurqhoua
   - Navigate to **Table Editor**

2. **Find the User:**
   - Go to the `auth` schema > `users` table
   - Find the user with email `<EMAIL>`
   - Copy their `id` (UUID)

3. **Update User Profile:**
   - Go to the `public` schema > `user_profiles` table
   - Find the row with the matching `id` or create a new row
   - Set the `role` column to `'admin'`
   - Save the changes

## Method 3: Using SQL Query (Advanced)

You can also run this SQL query directly in the Supabase SQL Editor:

```sql
-- First, find the user ID
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- Then update or insert the user profile (replace USER_ID with actual UUID)
INSERT INTO public.user_profiles (id, email, role, is_active, created_at, updated_at)
VALUES (
  'USER_ID_FROM_ABOVE', 
  '<EMAIL>', 
  'admin', 
  true, 
  NOW(), 
  NOW()
)
ON CONFLICT (id) 
DO UPDATE SET 
  role = 'admin',
  updated_at = NOW();
```

## Verification

After elevation, verify the user has admin status:

```sql
SELECT 
  up.email,
  up.role,
  up.is_active,
  up.created_at,
  up.updated_at
FROM public.user_profiles up
WHERE up.email = '<EMAIL>';
```

## Troubleshooting

### User Not Found
- Make sure the user has registered/signed up first
- Check the exact email spelling
- Verify the user exists in `auth.users` table

### Permission Errors
- Ensure you're using the correct service role key
- Check that RLS policies allow the operation
- Verify the service role has proper permissions

### Script Errors
- Make sure you're in the `pressure-max-api` directory
- Check that all dependencies are installed: `npm install`
- Verify the `.env` file has correct Supabase configuration

## Security Notes

- ⚠️ **Never expose the service role key in client-side code**
- ✅ Only run admin scripts on secure servers
- ✅ Log all admin role changes for audit purposes
- ✅ Regularly review admin user list

## Available Roles

The system supports these roles:
- `user` - Standard user (default)
- `admin` - Full administrative access
- `moderator` - Limited administrative access
