const { supabaseAdmin } = require('../config/supabase');

class FacebookRateLimiter {
  constructor() {
    // Rate limiting configuration
    this.limits = {
      // Marketing API limits per hour
      marketing_api: {
        calls_per_hour: 200,
        calls_per_account_per_hour: 25,
        window_ms: 60 * 60 * 1000 // 1 hour
      },
      // Graph API limits
      graph_api: {
        calls_per_hour: 600,
        window_ms: 60 * 60 * 1000 // 1 hour
      },
      // Batch API limits
      batch_api: {
        requests_per_batch: 50,
        calls_per_hour: 100,
        window_ms: 60 * 60 * 1000 // 1 hour
      }
    };

    // In-memory rate limit tracking
    this.rateLimitData = new Map();
    
    // Error retry configuration
    this.retryConfig = {
      max_retries: 3,
      base_delay: 1000, // 1 second
      max_delay: 30000, // 30 seconds
      backoff_multiplier: 2
    };
  }

  /**
   * Check if request is within rate limits
   */
  checkRateLimit(apiType, identifier = 'global') {
    const key = `${apiType}_${identifier}`;
    const limit = this.limits[apiType];
    
    if (!limit) {
      throw new Error(`Unknown API type: ${apiType}`);
    }

    const now = Date.now();
    const windowStart = now - limit.window_ms;

    // Get or create rate limit data
    if (!this.rateLimitData.has(key)) {
      this.rateLimitData.set(key, []);
    }

    const requests = this.rateLimitData.get(key);
    
    // Remove old requests outside the window
    const validRequests = requests.filter(timestamp => timestamp > windowStart);
    this.rateLimitData.set(key, validRequests);

    // Check if we're within limits
    const maxCalls = apiType === 'marketing_api' && identifier !== 'global' 
      ? limit.calls_per_account_per_hour 
      : limit.calls_per_hour;

    if (validRequests.length >= maxCalls) {
      const oldestRequest = Math.min(...validRequests);
      const resetTime = oldestRequest + limit.window_ms;
      
      return {
        allowed: false,
        resetTime,
        remaining: 0,
        limit: maxCalls
      };
    }

    return {
      allowed: true,
      remaining: maxCalls - validRequests.length,
      limit: maxCalls
    };
  }

  /**
   * Record a successful API request
   */
  recordRequest(apiType, identifier = 'global') {
    const key = `${apiType}_${identifier}`;
    const now = Date.now();

    if (!this.rateLimitData.has(key)) {
      this.rateLimitData.set(key, []);
    }

    const requests = this.rateLimitData.get(key);
    requests.push(now);
  }

  /**
   * Make rate-limited Facebook API request
   */
  async makeRequest(url, options = {}, apiType = 'graph_api', identifier = 'global') {
    const maxRetries = this.retryConfig.max_retries;
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        // Check rate limits
        const rateLimitCheck = this.checkRateLimit(apiType, identifier);
        
        if (!rateLimitCheck.allowed) {
          const waitTime = rateLimitCheck.resetTime - Date.now();
          console.log(`Rate limit exceeded. Waiting ${waitTime}ms before retry.`);
          
          if (waitTime > 0) {
            await this.sleep(Math.min(waitTime, 60000)); // Max 1 minute wait
          }
          
          // Recheck after waiting
          const recheckLimit = this.checkRateLimit(apiType, identifier);
          if (!recheckLimit.allowed) {
            throw new Error('Rate limit exceeded and wait time expired');
          }
        }

        // Make the request
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...options.headers
          },
          ...options
        });

        const data = await response.json();

        // Handle Facebook API errors
        if (data.error) {
          const error = data.error;
          
          // Check if it's a rate limit error
          if (error.code === 4 || error.code === 17 || error.code === 32) {
            console.log(`Facebook rate limit error (code ${error.code}). Retrying...`);
            
            // Wait before retry
            const delay = this.calculateBackoffDelay(attempt);
            await this.sleep(delay);
            
            attempt++;
            continue;
          }
          
          // Check if it's a temporary error that should be retried
          if (error.code === 1 || error.code === 2 || error.code === 341) {
            console.log(`Facebook temporary error (code ${error.code}). Retrying...`);
            
            const delay = this.calculateBackoffDelay(attempt);
            await this.sleep(delay);
            
            attempt++;
            continue;
          }
          
          // Non-retryable error
          throw new Error(`Facebook API error: ${error.message} (Code: ${error.code})`);
        }

        // Record successful request
        this.recordRequest(apiType, identifier);

        return data;
      } catch (error) {
        if (attempt >= maxRetries) {
          console.error(`Facebook API request failed after ${maxRetries} retries:`, error);
          throw error;
        }

        console.log(`Request attempt ${attempt + 1} failed:`, error.message);
        
        // Calculate backoff delay
        const delay = this.calculateBackoffDelay(attempt);
        await this.sleep(delay);
        
        attempt++;
      }
    }
  }

  /**
   * Make batch request to Facebook API
   */
  async makeBatchRequest(requests, accessToken, apiType = 'batch_api') {
    try {
      // Check rate limits for batch API
      const rateLimitCheck = this.checkRateLimit(apiType);
      
      if (!rateLimitCheck.allowed) {
        const waitTime = rateLimitCheck.resetTime - Date.now();
        if (waitTime > 0) {
          await this.sleep(Math.min(waitTime, 60000));
        }
      }

      // Limit batch size
      const maxBatchSize = this.limits.batch_api.requests_per_batch;
      if (requests.length > maxBatchSize) {
        throw new Error(`Batch size ${requests.length} exceeds maximum ${maxBatchSize}`);
      }

      // Prepare batch request
      const batchData = requests.map(req => ({
        method: req.method || 'GET',
        relative_url: req.relative_url
      }));

      const url = `https://graph.facebook.com/v18.0/`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          access_token: accessToken,
          batch: JSON.stringify(batchData)
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook batch API error: ${data.error.message}`);
      }

      // Record successful batch request
      this.recordRequest(apiType);

      // Parse batch responses
      return data.map(item => {
        if (item.code === 200) {
          return JSON.parse(item.body);
        } else {
          return {
            error: {
              code: item.code,
              message: item.body
            }
          };
        }
      });
    } catch (error) {
      console.error('Batch request error:', error);
      throw error;
    }
  }

  /**
   * Calculate exponential backoff delay
   */
  calculateBackoffDelay(attempt) {
    const delay = this.retryConfig.base_delay * Math.pow(this.retryConfig.backoff_multiplier, attempt);
    return Math.min(delay, this.retryConfig.max_delay);
  }

  /**
   * Sleep utility function
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get rate limit status for monitoring
   */
  getRateLimitStatus() {
    const status = {};
    
    for (const [key, requests] of this.rateLimitData.entries()) {
      const [apiType, identifier] = key.split('_', 2);
      const limit = this.limits[apiType];
      
      if (limit) {
        const now = Date.now();
        const windowStart = now - limit.window_ms;
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        
        const maxCalls = apiType === 'marketing_api' && identifier !== 'global' 
          ? limit.calls_per_account_per_hour 
          : limit.calls_per_hour;

        status[key] = {
          used: validRequests.length,
          limit: maxCalls,
          remaining: maxCalls - validRequests.length,
          resetTime: validRequests.length > 0 ? Math.min(...validRequests) + limit.window_ms : null
        };
      }
    }
    
    return status;
  }

  /**
   * Clear rate limit data (for testing)
   */
  clearRateLimitData() {
    this.rateLimitData.clear();
  }

  /**
   * Log rate limit usage to database
   */
  async logRateLimitUsage(workspaceId, apiType, endpoint, success = true) {
    try {
      await supabaseAdmin
        .from('security_events')
        .insert({
          user_id: null, // System event
          event_type: 'facebook_api_usage',
          event_data: {
            workspace_id: workspaceId,
            api_type: apiType,
            endpoint: endpoint,
            success: success,
            timestamp: new Date(),
            rate_limit_status: this.getRateLimitStatus()
          },
          created_at: new Date()
        });
    } catch (error) {
      console.error('Failed to log rate limit usage:', error);
    }
  }
}

module.exports = new FacebookRateLimiter();
