const express = require('express');
const { supabaseAdmin } = require('../config/supabase');
const { authenticateToken, requireAdmin, requireModerator } = require('../middleware/auth');
const router = express.Router();

/**
 * Get all template categories (Public access for browsing)
 */
router.get('/categories', async (req, res) => {
  try {
    const { data: categories, error } = await supabaseAdmin
      .from('template_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      return res.status(500).json({
        error: 'Failed to fetch categories',
        code: 'CATEGORIES_FETCH_FAILED'
      });
    }

    res.json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Categories fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch categories',
      code: 'CATEGORIES_FETCH_FAILED'
    });
  }
});

/**
 * Get all templates with optional filtering (Public access for browsing)
 */
router.get('/', async (req, res) => {
  try {
    const { category_id, search, limit = 50, offset = 0 } = req.query;

    let query = supabaseAdmin
      .from('campaign_templates')
      .select(`
        *,
        template_categories(name, description),
        template_creatives(*)
      `)
      .eq('is_active', true);

    // Apply filters
    if (category_id) {
      query = query.eq('category_id', category_id);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply pagination
    query = query
      .order('usage_count', { ascending: false })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    const { data: templates, error } = await query;

    if (error) {
      return res.status(500).json({
        error: 'Failed to fetch templates',
        code: 'TEMPLATES_FETCH_FAILED'
      });
    }

    res.json({
      success: true,
      templates,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: templates.length
      }
    });
  } catch (error) {
    console.error('Templates fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch templates',
      code: 'TEMPLATES_FETCH_FAILED'
    });
  }
});

/**
 * Get single template by ID (Public access for browsing)
 */
router.get('/:templateId', async (req, res) => {
  try {
    const { templateId } = req.params;

    const { data: template, error } = await supabaseAdmin
      .from('campaign_templates')
      .select(`
        *,
        template_categories(name, description),
        template_creatives(*)
      `)
      .eq('id', templateId)
      .eq('is_active', true)
      .single();

    if (error || !template) {
      return res.status(404).json({
        error: 'Template not found',
        code: 'TEMPLATE_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      template
    });
  } catch (error) {
    console.error('Template fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch template',
      code: 'TEMPLATE_FETCH_FAILED'
    });
  }
});

/**
 * Create new template (Admin only)
 */
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      category_id,
      name,
      description,
      objective,
      suggested_copy,
      suggested_targeting,
      suggested_lead_form,
      suggested_budget_min,
      suggested_budget_max
    } = req.body;

    // Validate required fields
    if (!name || !category_id || !objective) {
      return res.status(400).json({
        error: 'Name, category, and objective are required',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    const { data: template, error } = await supabaseAdmin
      .from('campaign_templates')
      .insert({
        category_id,
        name,
        description,
        objective,
        suggested_copy,
        suggested_targeting,
        suggested_lead_form,
        suggested_budget_min,
        suggested_budget_max,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        error: 'Failed to create template',
        details: error.message,
        code: 'TEMPLATE_CREATE_FAILED'
      });
    }

    res.json({
      success: true,
      template,
      message: 'Template created successfully'
    });
  } catch (error) {
    console.error('Template creation error:', error);
    res.status(500).json({
      error: 'Failed to create template',
      code: 'TEMPLATE_CREATE_FAILED'
    });
  }
});

/**
 * Update template (Admin only)
 */
router.put('/:templateId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { templateId } = req.params;
    const updateData = req.body;

    // Remove fields that shouldn't be updated directly
    delete updateData.id;
    delete updateData.created_at;
    delete updateData.usage_count;

    const { data: template, error } = await supabaseAdmin
      .from('campaign_templates')
      .update({
        ...updateData,
        updated_at: new Date()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        error: 'Failed to update template',
        details: error.message,
        code: 'TEMPLATE_UPDATE_FAILED'
      });
    }

    if (!template) {
      return res.status(404).json({
        error: 'Template not found',
        code: 'TEMPLATE_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      template,
      message: 'Template updated successfully'
    });
  } catch (error) {
    console.error('Template update error:', error);
    res.status(500).json({
      error: 'Failed to update template',
      code: 'TEMPLATE_UPDATE_FAILED'
    });
  }
});

/**
 * Delete template (Admin only)
 */
router.delete('/:templateId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { templateId } = req.params;

    // Soft delete by setting is_active to false
    const { data: template, error } = await supabaseAdmin
      .from('campaign_templates')
      .update({
        is_active: false,
        updated_at: new Date()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        error: 'Failed to delete template',
        details: error.message,
        code: 'TEMPLATE_DELETE_FAILED'
      });
    }

    if (!template) {
      return res.status(404).json({
        error: 'Template not found',
        code: 'TEMPLATE_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      message: 'Template deleted successfully'
    });
  } catch (error) {
    console.error('Template deletion error:', error);
    res.status(500).json({
      error: 'Failed to delete template',
      code: 'TEMPLATE_DELETE_FAILED'
    });
  }
});

/**
 * Duplicate template (Admin only)
 */
router.post('/:templateId/duplicate', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { templateId } = req.params;
    const { name } = req.body;

    // Get original template
    const { data: originalTemplate, error: fetchError } = await supabaseAdmin
      .from('campaign_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (fetchError || !originalTemplate) {
      return res.status(404).json({
        error: 'Template not found',
        code: 'TEMPLATE_NOT_FOUND'
      });
    }

    // Create duplicate
    const duplicateData = {
      ...originalTemplate,
      name: name || `${originalTemplate.name} (Copy)`,
      usage_count: 0,
      avg_cpl: null,
      avg_ctr: null
    };

    delete duplicateData.id;
    delete duplicateData.created_at;
    delete duplicateData.updated_at;

    const { data: newTemplate, error: createError } = await supabaseAdmin
      .from('campaign_templates')
      .insert(duplicateData)
      .select()
      .single();

    if (createError) {
      return res.status(400).json({
        error: 'Failed to duplicate template',
        details: createError.message,
        code: 'TEMPLATE_DUPLICATE_FAILED'
      });
    }

    res.json({
      success: true,
      template: newTemplate,
      message: 'Template duplicated successfully'
    });
  } catch (error) {
    console.error('Template duplication error:', error);
    res.status(500).json({
      error: 'Failed to duplicate template',
      code: 'TEMPLATE_DUPLICATE_FAILED'
    });
  }
});

/**
 * Update template usage count (internal use)
 */
router.post('/:templateId/usage', authenticateToken, async (req, res) => {
  try {
    const { templateId } = req.params;

    const { data: template, error } = await supabaseAdmin
      .from('campaign_templates')
      .update({
        usage_count: supabaseAdmin.raw('usage_count + 1'),
        updated_at: new Date()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      return res.status(400).json({
        error: 'Failed to update usage count',
        code: 'USAGE_UPDATE_FAILED'
      });
    }

    res.json({
      success: true,
      usage_count: template.usage_count
    });
  } catch (error) {
    console.error('Usage update error:', error);
    res.status(500).json({
      error: 'Failed to update usage count',
      code: 'USAGE_UPDATE_FAILED'
    });
  }
});

/**
 * Get template analytics (Admin only)
 */
router.get('/:templateId/analytics', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { templateId } = req.params;

    // Get template with usage stats
    const { data: template, error: templateError } = await supabaseAdmin
      .from('campaign_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (templateError || !template) {
      return res.status(404).json({
        error: 'Template not found',
        code: 'TEMPLATE_NOT_FOUND'
      });
    }

    // Get campaigns using this template
    const { data: campaigns, error: campaignsError } = await supabaseAdmin
      .from('campaigns')
      .select('id, status, leads_count, spend, impressions, clicks')
      .eq('source_template_id', templateId);

    if (campaignsError) {
      console.error('Campaigns fetch error:', campaignsError);
    }

    // Calculate analytics
    const analytics = {
      total_campaigns: campaigns?.length || 0,
      active_campaigns: campaigns?.filter(c => c.status === 'ACTIVE').length || 0,
      total_leads: campaigns?.reduce((sum, c) => sum + (c.leads_count || 0), 0) || 0,
      total_spend: campaigns?.reduce((sum, c) => sum + (c.spend || 0), 0) || 0,
      total_impressions: campaigns?.reduce((sum, c) => sum + (c.impressions || 0), 0) || 0,
      total_clicks: campaigns?.reduce((sum, c) => sum + (c.clicks || 0), 0) || 0
    };

    analytics.avg_cpl = analytics.total_leads > 0 ? analytics.total_spend / analytics.total_leads : 0;
    analytics.avg_ctr = analytics.total_impressions > 0 ? (analytics.total_clicks / analytics.total_impressions) * 100 : 0;

    res.json({
      success: true,
      template,
      analytics
    });
  } catch (error) {
    console.error('Template analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch template analytics',
      code: 'ANALYTICS_FETCH_FAILED'
    });
  }
});

module.exports = router;
