// Global variables
let allTemplates = [];
let allCategories = [];

// Facebook Ads page specific functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeFacebookAdsPage();
});

// Load templates from API
async function loadTemplatesFromAPI() {
    try {
        console.log('Loading templates from API...');

        // Show loading state
        const templatesGrid = document.querySelector('.templates-grid');
        if (templatesGrid) {
            templatesGrid.classList.add('loading');
            templatesGrid.innerHTML = '<div class="loading-spinner"></div>';
        }

        // Load categories
        const categoriesResponse = await fetch('/api/v1/templates/categories');
        if (categoriesResponse.ok) {
            const categoriesData = await categoriesResponse.json();
            allCategories = categoriesData.categories || [];
            console.log('Loaded categories:', allCategories);
        }

        // Load templates
        const templatesResponse = await fetch('/api/v1/templates');
        if (templatesResponse.ok) {
            const templatesData = await templatesResponse.json();
            allTemplates = templatesData.templates || [];
            console.log('Loaded templates:', allTemplates);

            // Remove loading state and render templates
            if (templatesGrid) {
                templatesGrid.classList.remove('loading');
            }
            renderTemplates(allTemplates);

            showNotification(`Loaded ${allTemplates.length} pressure washing templates`, 'success');
        } else {
            console.error('Failed to load templates:', templatesResponse.status);
            if (templatesGrid) {
                templatesGrid.classList.remove('loading');
                templatesGrid.innerHTML = '<p style="text-align: center; color: #888;">Failed to load templates</p>';
            }
            showNotification('Failed to load templates', 'error');
        }

    } catch (error) {
        console.error('Error loading templates:', error);
        const templatesGrid = document.querySelector('.templates-grid');
        if (templatesGrid) {
            templatesGrid.classList.remove('loading');
            templatesGrid.innerHTML = '<p style="text-align: center; color: #888;">Error loading templates</p>';
        }
        showNotification('Error loading templates', 'error');
    }
}

// Render templates on the page
function renderTemplates(templates) {
    const templatesGrid = document.querySelector('.templates-grid');
    if (!templatesGrid) return;

    templatesGrid.innerHTML = '';

    templates.forEach(template => {
        const templateCard = createTemplateCard(template);
        templatesGrid.appendChild(templateCard);
    });
}

// Create template card element
function createTemplateCard(template) {
    const card = document.createElement('div');
    card.className = 'template-card';
    card.setAttribute('data-template', template.id);

    const categoryName = allCategories.find(cat => cat.id === template.category_id)?.name || 'General';
    const suggestedCopy = template.suggested_copy || {};
    const headlines = suggestedCopy.headlines || [];
    const primaryText = suggestedCopy.primary_text || [];

    card.innerHTML = `
        <div class="template-preview">
            <div class="template-image">
                <div class="placeholder-image">
                    <i data-lucide="image"></i>
                </div>
            </div>
            <div class="platform-icon">
                <span>📘 Facebook</span>
            </div>
        </div>
        <div class="template-content">
            <div class="template-header">
                <h3 class="template-title">${template.name}</h3>
                <div class="template-category">${categoryName}</div>
            </div>
            <p class="template-description">${template.description || ''}</p>
            <div class="template-stats">
                <div class="stat">
                    <span class="stat-label">Usage</span>
                    <span class="stat-value">${template.usage_count || 0}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Avg CPL</span>
                    <span class="stat-value">${template.avg_cpl ? '$' + template.avg_cpl.toFixed(2) : 'N/A'}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">CTR</span>
                    <span class="stat-value">${template.avg_ctr ? template.avg_ctr.toFixed(2) + '%' : 'N/A'}</span>
                </div>
            </div>
            <div class="template-actions">
                <button class="btn btn-secondary" onclick="previewTemplate('${template.id}')">
                    <i data-lucide="eye"></i>
                    Preview
                </button>
                <button class="btn btn-primary" onclick="launchCampaign('${template.id}')">
                    <i data-lucide="rocket"></i>
                    Launch
                </button>
            </div>
        </div>
    `;

    return card;
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

async function initializeFacebookAdsPage() {
    // Load templates from API first
    await loadTemplatesFromAPI();

    // Platform tab switching
    const platformTabs = document.querySelectorAll('.platform-tab');
    platformTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            platformTabs.forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            this.classList.add('active');

            // Filter templates based on platform
            const platform = this.textContent.trim();
            filterTemplatesByPlatform(platform);
        });
    });
    
    // Template search functionality
    const templateSearch = document.getElementById('templateSearch');
    if (templateSearch) {
        templateSearch.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterTemplatesBySearch(searchTerm);
        });
    }
    
    // Template card interactions
    const templateCards = document.querySelectorAll('.template-card');
    templateCards.forEach(card => {
        // Launch Campaign buttons
        const launchBtn = card.querySelector('.template-btn-primary');
        if (launchBtn) {
            launchBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const templateId = card.getAttribute('data-template');
                launchCampaign(templateId);
            });
        }
        
        // Details buttons
        const detailsBtn = card.querySelector('.template-btn-secondary');
        if (detailsBtn) {
            detailsBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const templateId = card.getAttribute('data-template');
                showTemplateDetails(templateId);
            });
        }
        
        // Card click for preview
        card.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template');
            previewTemplate(templateId);
        });
    });
    
    // Import all templates button
    const importAllBtn = document.querySelector('.template-actions .btn-primary');
    if (importAllBtn) {
        importAllBtn.addEventListener('click', function() {
            importAllTemplates();
        });
    }
    
    // Create custom template button
    const createCustomBtn = document.querySelector('.dashboard-actions .btn-primary');
    if (createCustomBtn) {
        createCustomBtn.addEventListener('click', function() {
            createCustomTemplate();
        });
    }
    
    // Filter button
    const filterBtn = document.querySelector('.template-actions .btn-secondary');
    if (filterBtn) {
        filterBtn.addEventListener('click', function() {
            showFilterOptions();
        });
    }
}

// Filter templates by platform
function filterTemplatesByPlatform(platform) {
    const templateCards = document.querySelectorAll('.template-card');
    
    templateCards.forEach(card => {
        if (platform === 'All Platforms') {
            card.style.display = 'block';
        } else if (platform === 'Facebook & Instagram') {
            // Show only Facebook/Instagram templates
            const platformIcon = card.querySelector('.platform-icon span');
            if (platformIcon && platformIcon.textContent.includes('Facebook')) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        } else if (platform === 'Google Ads') {
            // Show only Google Ads templates (none in current set)
            card.style.display = 'none';
        }
    });
    
    console.log('Filtered templates by platform:', platform);
}

// Filter templates by search term
function filterTemplatesBySearch(searchTerm) {
    const templateCards = document.querySelectorAll('.template-card');
    
    templateCards.forEach(card => {
        const title = card.querySelector('.template-title').textContent.toLowerCase();
        const templateId = card.getAttribute('data-template').toLowerCase();
        
        if (title.includes(searchTerm) || templateId.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
    
    console.log('Filtered templates by search:', searchTerm);
}

// Launch campaign with selected template
function launchCampaign(templateId) {
    console.log('Launching campaign with template:', templateId);
    
    // Show loading state
    showNotification('Preparing campaign launch...', 'info');
    
    // Simulate API call to launch campaign
    setTimeout(() => {
        // Template data mapping
        const templateData = getTemplateData(templateId);
        
        if (templateData) {
            showCampaignLaunchModal(templateData);
        } else {
            showNotification('Template not found. Please try again.', 'error');
        }
    }, 1000);
}

// Show template details
function showTemplateDetails(templateId) {
    console.log('Showing details for template:', templateId);
    
    const templateData = getTemplateData(templateId);
    if (templateData) {
        showTemplateDetailsModal(templateData);
    } else {
        showNotification('Template details not available.', 'warning');
    }
}

// Preview template
function previewTemplate(templateId) {
    console.log('Previewing template:', templateId);
    
    const templateData = getTemplateData(templateId);
    if (templateData) {
        showTemplatePreviewModal(templateData);
    }
}

// Get template data by ID
function getTemplateData(templateId) {
    // Find template in loaded data
    const template = allTemplates.find(t => t.id === templateId);
    if (!template) return null;

    const categoryName = allCategories.find(cat => cat.id === template.category_id)?.name || 'General';
    const suggestedCopy = template.suggested_copy || {};
    const suggestedTargeting = template.suggested_targeting || {};
    const suggestedLeadForm = template.suggested_lead_form || {};

    // Convert API template format to expected format
    return {
        id: template.id,
        name: template.name,
        category: categoryName,
        description: template.description,
        pricing: template.suggested_budget_min && template.suggested_budget_max
            ? `$${template.suggested_budget_min} - $${template.suggested_budget_max}`
            : 'Custom Quote',
        targetAudience: suggestedTargeting.detailed_targeting?.join(', ') || 'Homeowners',
        platforms: ['Facebook', 'Instagram'],
        performance: {
            ctr: template.avg_ctr ? template.avg_ctr.toFixed(1) + '%' : 'N/A',
            cpc: 'N/A',
            conversions: template.usage_count ? `${template.usage_count} uses` : '0 uses'
        },
        creative: {
            headline: suggestedCopy.headlines?.[0] || template.name,
            description: suggestedCopy.primary_text?.[0] || template.description,
            callToAction: 'Get Quote'
        },
        leadForm: suggestedLeadForm,
        targeting: suggestedTargeting
    };
}

// Show campaign launch modal
function showCampaignLaunchModal(templateData) {
    const modal = createModal('Launch Campaign', `
        <div class="campaign-launch-content">
            <h3>Launch "${templateData.name}"</h3>
            <p>You're about to launch a campaign using this template:</p>
            
            <div class="template-summary">
                <div class="summary-item">
                    <strong>Category:</strong> ${templateData.category}
                </div>
                <div class="summary-item">
                    <strong>Target Audience:</strong> ${templateData.targetAudience}
                </div>
                <div class="summary-item">
                    <strong>Pricing:</strong> ${templateData.pricing}
                </div>
                <div class="summary-item">
                    <strong>Expected CTR:</strong> ${templateData.performance.ctr}
                </div>
            </div>
            
            <div class="campaign-options">
                <label>
                    <input type="text" placeholder="Campaign Name" value="${templateData.name} - Campaign">
                </label>
                <label>
                    <select>
                        <option>Facebook & Instagram</option>
                        <option>Facebook Only</option>
                        <option>Instagram Only</option>
                    </select>
                </label>
                <label>
                    <input type="number" placeholder="Daily Budget ($)" value="50">
                </label>
            </div>
            
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button class="btn btn-primary" onclick="confirmCampaignLaunch('${templateData.id}')">Launch Campaign</button>
            </div>
        </div>
    `);
    
    document.body.appendChild(modal);
}

// Show template details modal
function showTemplateDetailsModal(templateData) {
    const modal = createModal('Template Details', `
        <div class="template-details-content">
            <h3>${templateData.name}</h3>
            <p>${templateData.description}</p>
            
            <div class="details-grid">
                <div class="detail-section">
                    <h4>Performance Metrics</h4>
                    <ul>
                        <li>Click-through Rate: ${templateData.performance.ctr}</li>
                        <li>Cost per Click: ${templateData.performance.cpc}</li>
                        <li>Total Imports: ${templateData.performance.conversions}</li>
                    </ul>
                </div>
                
                <div class="detail-section">
                    <h4>Creative Elements</h4>
                    <ul>
                        <li>Headline: "${templateData.creative.headline}"</li>
                        <li>Description: "${templateData.creative.description}"</li>
                        <li>Call to Action: ${templateData.creative.callToAction}</li>
                    </ul>
                </div>
                
                <div class="detail-section">
                    <h4>Targeting</h4>
                    <ul>
                        <li>Audience: ${templateData.targetAudience}</li>
                        <li>Platforms: ${templateData.platforms.join(', ')}</li>
                        <li>Pricing: ${templateData.pricing}</li>
                    </ul>
                </div>
            </div>
            
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal()">Close</button>
                <button class="btn btn-primary" onclick="launchCampaign('${templateData.id}'); closeModal();">Launch Campaign</button>
            </div>
        </div>
    `);
    
    document.body.appendChild(modal);
}

// Show template preview modal
function showTemplatePreviewModal(templateData) {
    const modal = createModal('Template Preview', `
        <div class="template-preview-content">
            <h3>Preview: ${templateData.name}</h3>
            
            <div class="ad-preview">
                <div class="ad-preview-header">
                    <div class="ad-preview-profile">
                        <div class="profile-pic"></div>
                        <div class="profile-info">
                            <div class="profile-name">Your Business Name</div>
                            <div class="profile-sponsored">Sponsored</div>
                        </div>
                    </div>
                </div>
                
                <div class="ad-preview-content-area">
                    <div class="ad-preview-text">${templateData.creative.description}</div>
                    <div class="ad-preview-image">
                        <div class="preview-placeholder">Ad Image Preview</div>
                    </div>
                    <div class="ad-preview-cta">
                        <button class="preview-cta-btn">${templateData.creative.callToAction}</button>
                    </div>
                </div>
            </div>
            
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal()">Close</button>
                <button class="btn btn-primary" onclick="launchCampaign('${templateData.id}'); closeModal();">Use This Template</button>
            </div>
        </div>
    `);
    
    document.body.appendChild(modal);
}

// Create modal helper function
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>${title}</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    // Add modal styles if not already added
    if (!document.querySelector('#modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'modal-styles';
        styles.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            }
            
            .modal-content {
                background: #111111;
                border: 1px solid #2a2a2a;
                border-radius: 12px;
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                animation: slideUp 0.3s ease;
            }
            
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #2a2a2a;
            }
            
            .modal-header h2 {
                color: #ffffff;
                margin: 0;
            }
            
            .modal-close {
                background: none;
                border: none;
                color: #888888;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
            }
            
            .modal-close:hover {
                background: #2a2a2a;
                color: #ffffff;
            }
            
            .modal-body {
                padding: 20px;
                color: #cccccc;
            }
            
            .modal-actions {
                display: flex;
                gap: 12px;
                justify-content: flex-end;
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid #2a2a2a;
            }
            
            .template-summary, .details-grid {
                margin: 20px 0;
            }
            
            .summary-item, .detail-section {
                margin-bottom: 12px;
                padding: 12px;
                background: #1a1a1a;
                border-radius: 6px;
            }
            
            .detail-section h4 {
                color: #ffffff;
                margin-bottom: 8px;
            }
            
            .detail-section ul {
                list-style: none;
                padding: 0;
            }
            
            .detail-section li {
                padding: 4px 0;
                border-bottom: 1px solid #2a2a2a;
            }
            
            .campaign-options {
                display: flex;
                flex-direction: column;
                gap: 12px;
                margin: 20px 0;
            }
            
            .campaign-options input, .campaign-options select {
                background: #1a1a1a;
                border: 1px solid #2a2a2a;
                border-radius: 6px;
                padding: 10px;
                color: #ffffff;
                font-size: 14px;
            }
            
            .ad-preview {
                background: #1a1a1a;
                border-radius: 8px;
                padding: 16px;
                margin: 20px 0;
            }
            
            .ad-preview-header {
                display: flex;
                align-items: center;
                margin-bottom: 12px;
            }
            
            .ad-preview-profile {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .profile-pic {
                width: 40px;
                height: 40px;
                background: #3b82f6;
                border-radius: 50%;
            }
            
            .profile-name {
                font-weight: 600;
                color: #ffffff;
            }
            
            .profile-sponsored {
                font-size: 12px;
                color: #888888;
            }
            
            .ad-preview-text {
                margin-bottom: 12px;
                line-height: 1.5;
            }
            
            .preview-placeholder {
                background: #2a2a2a;
                padding: 40px;
                text-align: center;
                border-radius: 6px;
                color: #888888;
                margin-bottom: 12px;
            }
            
            .preview-cta-btn {
                background: #3b82f6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 500;
                cursor: pointer;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            @keyframes slideUp {
                from { transform: translateY(20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    return modal;
}

// Close modal
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// Confirm campaign launch
function confirmCampaignLaunch(templateId) {
    closeModal();
    showNotification('Campaign launched successfully! Redirecting to campaign manager...', 'success');
    
    // Simulate redirect to campaign manager
    setTimeout(() => {
        console.log('Redirecting to campaign manager for template:', templateId);
        // Could redirect to campaign management page
    }, 2000);
}

// Import all templates
function importAllTemplates() {
    showNotification('Importing all templates...', 'info');
    
    setTimeout(() => {
        showNotification('All templates imported successfully!', 'success');
    }, 2000);
}

// Create custom template
function createCustomTemplate() {
    showNotification('Custom template creator coming soon!', 'info');
}

// Show filter options
function showFilterOptions() {
    showNotification('Filter options coming soon!', 'info');
}
