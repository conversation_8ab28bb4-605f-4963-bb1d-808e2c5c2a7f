const express = require('express');
const crypto = require('crypto');
const { supabaseAdmin } = require('../config/supabase');
const router = express.Router();

/**
 * Facebook Webhook Verification
 */
router.get('/facebook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  // Verify the webhook
  if (mode === 'subscribe' && token === process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN) {
    console.log('Facebook webhook verified successfully');
    res.status(200).send(challenge);
  } else {
    console.error('Facebook webhook verification failed');
    res.status(403).send('Forbidden');
  }
});

/**
 * Facebook Webhook Handler
 */
router.post('/facebook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    // Verify webhook signature
    const signature = req.get('X-Hub-Signature-256');
    if (!verifyFacebookSignature(req.body, signature)) {
      console.error('Invalid Facebook webhook signature');
      return res.status(401).send('Unauthorized');
    }

    const body = JSON.parse(req.body.toString());
    
    // Process webhook events
    if (body.object === 'page') {
      for (const entry of body.entry) {
        await processPageEntry(entry);
      }
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Facebook webhook error:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * Verify Facebook webhook signature
 */
function verifyFacebookSignature(payload, signature) {
  if (!signature) {
    return false;
  }

  const expectedSignature = 'sha256=' + crypto
    .createHmac('sha256', process.env.FACEBOOK_APP_SECRET)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

/**
 * Process Facebook page entry
 */
async function processPageEntry(entry) {
  try {
    const pageId = entry.id;
    
    // Process different types of changes
    if (entry.changes) {
      for (const change of entry.changes) {
        await processPageChange(pageId, change);
      }
    }

    // Process leadgen events (lead form submissions)
    if (entry.leadgen) {
      for (const leadgen of entry.leadgen) {
        await processLeadgenEvent(pageId, leadgen);
      }
    }
  } catch (error) {
    console.error('Process page entry error:', error);
  }
}

/**
 * Process page change events
 */
async function processPageChange(pageId, change) {
  try {
    console.log(`Processing page change for ${pageId}:`, change);

    // Handle different field changes
    switch (change.field) {
      case 'leadgen':
        await processLeadgenChange(pageId, change);
        break;
      case 'ads':
        await processAdsChange(pageId, change);
        break;
      default:
        console.log(`Unhandled page change field: ${change.field}`);
    }
  } catch (error) {
    console.error('Process page change error:', error);
  }
}

/**
 * Process leadgen change events
 */
async function processLeadgenChange(pageId, change) {
  try {
    const leadgenId = change.value.leadgen_id;
    const adId = change.value.ad_id;
    const formId = change.value.form_id;
    const createdTime = new Date(change.value.created_time * 1000);

    console.log(`New lead received: ${leadgenId} from ad ${adId}`);

    // Find the campaign associated with this ad
    const { data: campaign, error: campaignError } = await supabaseAdmin
      .from('campaigns')
      .select('*, workspaces(*)')
      .eq('fb_ad_id', adId)
      .single();

    if (campaignError || !campaign) {
      console.error(`Campaign not found for ad ${adId}`);
      return;
    }

    // Get Facebook connection for this workspace
    const { data: connection, error: connectionError } = await supabaseAdmin
      .from('facebook_connections')
      .select('*')
      .eq('workspace_id', campaign.workspace_id)
      .single();

    if (connectionError || !connection) {
      console.error(`Facebook connection not found for workspace ${campaign.workspace_id}`);
      return;
    }

    // Fetch lead data from Facebook API
    const leadData = await fetchLeadData(leadgenId, connection.user_access_token_encrypted);

    if (leadData) {
      await storeLeadData(campaign, leadgenId, leadData, createdTime);
    }
  } catch (error) {
    console.error('Process leadgen change error:', error);
  }
}

/**
 * Process ads change events
 */
async function processAdsChange(pageId, change) {
  try {
    console.log(`Processing ads change for page ${pageId}:`, change);
    
    // Handle ad status changes, budget updates, etc.
    const adId = change.value.ad_id;
    
    if (adId) {
      // Update campaign status in database
      await updateCampaignFromFacebook(adId);
    }
  } catch (error) {
    console.error('Process ads change error:', error);
  }
}

/**
 * Process direct leadgen events
 */
async function processLeadgenEvent(pageId, leadgen) {
  try {
    console.log(`Processing leadgen event for page ${pageId}:`, leadgen);
    
    // This is an alternative way Facebook might send lead events
    // Process similar to processLeadgenChange
  } catch (error) {
    console.error('Process leadgen event error:', error);
  }
}

/**
 * Fetch lead data from Facebook API
 */
async function fetchLeadData(leadgenId, encryptedToken) {
  try {
    // Decrypt token (you'll need to implement token decryption)
    const FacebookTokenManager = require('../services/facebookTokenManager');
    const encryptedData = JSON.parse(encryptedToken);
    const accessToken = FacebookTokenManager.decryptToken(encryptedData);

    const response = await fetch(
      `https://graph.facebook.com/v18.0/${leadgenId}?access_token=${accessToken}`
    );
    
    const data = await response.json();

    if (data.error) {
      console.error('Facebook API error fetching lead:', data.error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Fetch lead data error:', error);
    return null;
  }
}

/**
 * Store lead data in database
 */
async function storeLeadData(campaign, fbLeadId, leadData, createdTime) {
  try {
    // Extract lead information
    const leadInfo = {
      full_name: '',
      email: '',
      phone: ''
    };

    // Parse field data from Facebook lead
    if (leadData.field_data) {
      for (const field of leadData.field_data) {
        switch (field.name.toLowerCase()) {
          case 'full_name':
          case 'name':
            leadInfo.full_name = field.values[0];
            break;
          case 'email':
            leadInfo.email = field.values[0];
            break;
          case 'phone_number':
          case 'phone':
            leadInfo.phone = field.values[0];
            break;
        }
      }
    }

    // Check if lead already exists
    const { data: existingLead, error: checkError } = await supabaseAdmin
      .from('leads')
      .select('id')
      .eq('fb_lead_id', fbLeadId)
      .single();

    if (existingLead) {
      console.log(`Lead ${fbLeadId} already exists, skipping`);
      return;
    }

    // Insert new lead
    const { data: newLead, error: insertError } = await supabaseAdmin
      .from('leads')
      .insert({
        campaign_id: campaign.id,
        workspace_id: campaign.workspace_id,
        fb_lead_id: fbLeadId,
        lead_data: leadData,
        full_name: leadInfo.full_name,
        email: leadInfo.email,
        phone: leadInfo.phone,
        status: 'new',
        created_time: createdTime,
        received_at: new Date()
      })
      .select()
      .single();

    if (insertError) {
      console.error('Failed to insert lead:', insertError);
      return;
    }

    // Update campaign lead count
    await supabaseAdmin
      .from('campaigns')
      .update({
        leads_count: campaign.leads_count + 1,
        updated_at: new Date()
      })
      .eq('id', campaign.id);

    console.log(`Successfully stored lead ${fbLeadId} for campaign ${campaign.id}`);

    // Send notification (implement as needed)
    await sendLeadNotification(campaign, newLead);

  } catch (error) {
    console.error('Store lead data error:', error);
  }
}

/**
 * Update campaign status from Facebook
 */
async function updateCampaignFromFacebook(adId) {
  try {
    // Find campaign by Facebook ad ID
    const { data: campaign, error } = await supabaseAdmin
      .from('campaigns')
      .select('*')
      .eq('fb_ad_id', adId)
      .single();

    if (error || !campaign) {
      console.log(`Campaign not found for ad ${adId}`);
      return;
    }

    // Fetch current ad status from Facebook
    // (Implementation depends on your Facebook service)
    console.log(`Updating campaign ${campaign.id} from Facebook ad ${adId}`);
    
  } catch (error) {
    console.error('Update campaign from Facebook error:', error);
  }
}

/**
 * Send lead notification
 */
async function sendLeadNotification(campaign, lead) {
  try {
    // Log notification event
    await supabaseAdmin
      .from('security_events')
      .insert({
        user_id: campaign.workspaces?.owner_id,
        event_type: 'new_lead_received',
        event_data: {
          campaign_id: campaign.id,
          lead_id: lead.id,
          lead_name: lead.full_name,
          lead_email: lead.email,
          timestamp: new Date()
        },
        created_at: new Date()
      });

    // Here you could implement:
    // - Email notifications
    // - SMS notifications
    // - Push notifications
    // - Slack/Discord webhooks
    
    console.log(`Lead notification sent for ${lead.full_name}`);
  } catch (error) {
    console.error('Send lead notification error:', error);
  }
}

module.exports = router;
