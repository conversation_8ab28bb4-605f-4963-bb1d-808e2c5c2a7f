const { supabase, supabaseAdmin } = require('../config/supabase');

/**
 * Middleware to authenticate Supabase JWT tokens
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access token required',
        code: 'TOKEN_REQUIRED'
      });
    }

    // Verify the JWT token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({
        error: 'Invalid or expired token',
        code: 'TOKEN_INVALID'
      });
    }

    // Get user profile for additional information
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Profile fetch error:', profileError);
      return res.status(401).json({
        error: 'User profile not found',
        code: 'PROFILE_NOT_FOUND'
      });
    }

    // Update last activity
    await supabaseAdmin
      .from('user_profiles')
      .update({ last_login: new Date() })
      .eq('id', user.id);

    // Attach user and profile to request
    req.user = {
      ...user,
      profile
    };

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({
      error: 'Authentication failed',
      code: 'AUTH_FAILED'
    });
  }
};

/**
 * Middleware to require specific roles
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user || !req.user.profile) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRole = req.user.profile.role;
    const requiredRoles = Array.isArray(roles) ? roles : [roles];

    if (!requiredRoles.includes(userRole)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: requiredRoles,
        current: userRole
      });
    }

    next();
  };
};

/**
 * Middleware to check if user has admin role
 */
const requireAdmin = requireRole('admin');

/**
 * Middleware to check if user has moderator or admin role
 */
const requireModerator = requireRole(['admin', 'moderator']);

/**
 * Middleware for optional authentication (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = JWTManager.extractTokenFromHeader(req);

    if (!token) {
      return next(); // Continue without authentication
    }

    const decoded = JWTManager.verifyAccessToken(token);
    
    if (decoded.type === 'access') {
      const { data: user, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', decoded.userId)
        .eq('is_active', true)
        .single();

      if (!error && user) {
        req.user = user;
        req.tokenPayload = decoded;
      }
    }
  } catch (error) {
    // Silently fail for optional auth
    console.log('Optional auth failed:', error.message);
  }
  
  next();
};

/**
 * Middleware to validate refresh token
 */
const validateRefreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ 
        error: 'Refresh token required',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    const tokenHash = JWTManager.hashRefreshToken(refreshToken);

    // Check if refresh token exists and is valid
    const { data: tokenRecord, error } = await supabase
      .from('refresh_tokens')
      .select('*')
      .eq('token_hash', tokenHash)
      .is('revoked_at', null)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !tokenRecord) {
      return res.status(401).json({ 
        error: 'Invalid or expired refresh token',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    // Get user associated with token
    const { data: user, error: userError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', tokenRecord.user_id)
      .eq('is_active', true)
      .single();

    if (userError || !user) {
      return res.status(401).json({ 
        error: 'User not found or inactive',
        code: 'USER_NOT_FOUND'
      });
    }

    req.user = user;
    req.refreshTokenRecord = tokenRecord;
    req.refreshToken = refreshToken;
    
    next();
  } catch (error) {
    console.error('Refresh token validation error:', error);
    return res.status(500).json({ 
      error: 'Token validation failed',
      code: 'VALIDATION_ERROR'
    });
  }
};

/**
 * Middleware to verify workspace ownership
 */
const verifyWorkspaceOwnership = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.workspaceId || req.body.workspace_id;

    if (!workspaceId) {
      return res.status(400).json({
        error: 'Workspace ID required',
        code: 'WORKSPACE_ID_REQUIRED'
      });
    }

    // Check if user owns the workspace
    const { data: workspace, error } = await supabaseAdmin
      .from('workspaces')
      .select('owner_id')
      .eq('id', workspaceId)
      .single();

    if (error || !workspace) {
      return res.status(404).json({
        error: 'Workspace not found',
        code: 'WORKSPACE_NOT_FOUND'
      });
    }

    if (workspace.owner_id !== userId) {
      return res.status(403).json({
        error: 'Workspace access denied',
        code: 'WORKSPACE_ACCESS_DENIED'
      });
    }

    // Attach workspace to request
    req.workspace = { id: workspaceId, owner_id: workspace.owner_id };
    next();
  } catch (error) {
    console.error('Workspace verification error:', error);
    return res.status(500).json({
      error: 'Workspace verification failed',
      code: 'WORKSPACE_VERIFICATION_FAILED'
    });
  }
};

/**
 * Middleware to check if user has Facebook connection for a workspace
 */
const requireFacebookAuth = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const workspaceId = req.params.workspaceId || req.body.workspace_id;

    if (!workspaceId) {
      return res.status(400).json({
        error: 'Workspace ID required',
        code: 'WORKSPACE_ID_REQUIRED'
      });
    }

    // Check Facebook connection for workspace
    const { data: connection, error } = await supabaseAdmin
      .from('facebook_connections')
      .select('*')
      .eq('workspace_id', workspaceId)
      .single();

    if (error || !connection || connection.connection_status !== 'connected') {
      return res.status(403).json({
        error: 'Facebook account connection required',
        code: 'FACEBOOK_AUTH_REQUIRED'
      });
    }

    req.facebookConnection = connection;
    next();
  } catch (error) {
    console.error('Facebook auth check error:', error);
    return res.status(500).json({
      error: 'Facebook authentication check failed',
      code: 'FACEBOOK_AUTH_CHECK_FAILED'
    });
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireModerator,
  verifyWorkspaceOwnership,
  requireFacebookAuth
};
