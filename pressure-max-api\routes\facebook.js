const express = require('express');
const { supabaseAdmin } = require('../config/supabase');
const { authenticateToken, verifyWorkspaceOwnership } = require('../middleware/auth');
const FacebookPermissionsService = require('../services/facebookPermissionsService');
const FacebookTokenManager = require('../services/facebookTokenManager');
const router = express.Router();

/**
 * Get Facebook connection status for workspace
 */
router.get('/connection/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;

    const { data: connection, error } = await supabaseAdmin
      .from('facebook_connections')
      .select('*')
      .eq('workspace_id', workspaceId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      return res.status(500).json({
        error: 'Failed to fetch connection status',
        code: 'CONNECTION_FETCH_FAILED'
      });
    }

    res.json({
      success: true,
      connected: !!connection,
      connection: connection || null
    });
  } catch (error) {
    console.error('Connection status error:', error);
    res.status(500).json({
      error: 'Failed to get connection status',
      code: 'CONNECTION_STATUS_FAILED'
    });
  }
});

/**
 * Connect Facebook account to workspace
 */
router.post('/connect/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;
    const { accessToken, userInfo } = req.body;

    if (!accessToken || !userInfo) {
      return res.status(400).json({
        error: 'Access token and user info required',
        code: 'MISSING_REQUIRED_DATA'
      });
    }

    // Store the connection
    const connection = await FacebookTokenManager.storeConnection(workspaceId, {
      userAccessToken: accessToken,
      facebookUserId: userInfo.id,
      facebookUserName: userInfo.name,
      permissions: userInfo.permissions || []
    });

    // Verify permissions
    const permissionCheck = await FacebookPermissionsService.verifyPermissions(workspaceId);

    res.json({
      success: true,
      connection,
      permissions: permissionCheck,
      message: 'Facebook account connected successfully'
    });
  } catch (error) {
    console.error('Facebook connect error:', error);
    res.status(500).json({
      error: 'Failed to connect Facebook account',
      details: error.message,
      code: 'FACEBOOK_CONNECT_FAILED'
    });
  }
});

/**
 * Get available ad accounts
 */
router.get('/ad-accounts/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;

    const adAccounts = await FacebookPermissionsService.getAvailableAdAccounts(workspaceId);

    res.json({
      success: true,
      adAccounts
    });
  } catch (error) {
    console.error('Get ad accounts error:', error);
    res.status(500).json({
      error: 'Failed to fetch ad accounts',
      details: error.message,
      code: 'AD_ACCOUNTS_FETCH_FAILED'
    });
  }
});

/**
 * Get available Facebook pages
 */
router.get('/pages/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;

    const pages = await FacebookPermissionsService.getAvailablePages(workspaceId);

    res.json({
      success: true,
      pages
    });
  } catch (error) {
    console.error('Get pages error:', error);
    res.status(500).json({
      error: 'Failed to fetch pages',
      details: error.message,
      code: 'PAGES_FETCH_FAILED'
    });
  }
});

/**
 * Select ad account and page for workspace
 */
router.post('/select-assets/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;
    const { adAccountId, pageId } = req.body;

    if (!adAccountId || !pageId) {
      return res.status(400).json({
        error: 'Ad account ID and page ID are required',
        code: 'MISSING_ASSET_IDS'
      });
    }

    // Verify access to selected assets
    const verification = await FacebookPermissionsService.completeVerification(
      workspaceId,
      adAccountId,
      pageId
    );

    if (!verification.success) {
      return res.status(403).json({
        error: verification.error,
        details: verification.details,
        code: 'ASSET_VERIFICATION_FAILED'
      });
    }

    // Get updated connection
    const { data: connection, error } = await supabaseAdmin
      .from('facebook_connections')
      .select('*')
      .eq('workspace_id', workspaceId)
      .single();

    if (error) {
      throw new Error('Failed to fetch updated connection');
    }

    res.json({
      success: true,
      connection,
      verification,
      message: 'Assets selected and verified successfully'
    });
  } catch (error) {
    console.error('Asset selection error:', error);
    res.status(500).json({
      error: 'Failed to select assets',
      details: error.message,
      code: 'ASSET_SELECTION_FAILED'
    });
  }
});

/**
 * Verify permissions for workspace
 */
router.post('/verify-permissions/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;

    const permissionCheck = await FacebookPermissionsService.verifyPermissions(workspaceId);

    res.json({
      success: true,
      permissions: permissionCheck
    });
  } catch (error) {
    console.error('Permission verification error:', error);
    res.status(500).json({
      error: 'Failed to verify permissions',
      details: error.message,
      code: 'PERMISSION_VERIFICATION_FAILED'
    });
  }
});

/**
 * Refresh Facebook token
 */
router.post('/refresh-token/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;

    const newToken = await FacebookTokenManager.refreshToken(workspaceId);

    res.json({
      success: true,
      message: 'Token refreshed successfully'
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      error: 'Failed to refresh token',
      details: error.message,
      code: 'TOKEN_REFRESH_FAILED'
    });
  }
});

/**
 * Disconnect Facebook account
 */
router.delete('/disconnect/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;

    await FacebookTokenManager.revokeConnection(workspaceId);

    res.json({
      success: true,
      message: 'Facebook account disconnected successfully'
    });
  } catch (error) {
    console.error('Disconnect error:', error);
    res.status(500).json({
      error: 'Failed to disconnect Facebook account',
      details: error.message,
      code: 'DISCONNECT_FAILED'
    });
  }
});

/**
 * Get permission request URL
 */
router.get('/permission-url', authenticateToken, async (req, res) => {
  try {
    const redirectUri = `${process.env.FRONTEND_URL}/facebook/callback`;
    const permissionUrl = FacebookPermissionsService.generatePermissionRequestUrl(redirectUri);

    res.json({
      success: true,
      permissionUrl
    });
  } catch (error) {
    console.error('Permission URL error:', error);
    res.status(500).json({
      error: 'Failed to generate permission URL',
      code: 'PERMISSION_URL_FAILED'
    });
  }
});

/**
 * Test Facebook API connection
 */
router.get('/test-connection/:workspaceId', authenticateToken, verifyWorkspaceOwnership, async (req, res) => {
  try {
    const { workspaceId } = req.params;

    // Get connection and test token
    const { token, connection } = await FacebookTokenManager.getDecryptedToken(workspaceId);

    // Test API call
    const response = await fetch(`https://graph.facebook.com/v18.0/me?access_token=${token}`);
    const data = await response.json();

    if (data.error) {
      throw new Error(`Facebook API error: ${data.error.message}`);
    }

    res.json({
      success: true,
      connection_status: 'active',
      user_info: data,
      message: 'Facebook connection is working properly'
    });
  } catch (error) {
    console.error('Connection test error:', error);
    res.status(500).json({
      error: 'Facebook connection test failed',
      details: error.message,
      code: 'CONNECTION_TEST_FAILED'
    });
  }
});

module.exports = router;
